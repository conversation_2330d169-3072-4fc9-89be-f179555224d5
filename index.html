<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Qur'anic Dictionary</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Droid Arabic Naskh font import -->
    <link href="https://fonts.googleapis.com/css2?family=Droid+Arabic+Naskh:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base styles for all screen sizes */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }
        
        body {
            background: #F5F5F5; /* New White background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            width: 100%;
            max-width: 500px;
            background-color: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 4px solid #deae35; /* Gold border */
            box-shadow: 0 10px 30px rgba(222, 174, 53, 0.15); /* Subtle gold shadow */
        }
        
        .header {
            background: black; /* Solid Black background */
            padding: 25px;
            text-align: center;
            position: relative;
        }
        
        .logo-container {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1); /* Shaded circle background */
            border-radius: 50%; /* Shaded circle border-radius */
            backdrop-filter: blur(5px); /* Shaded circle backdrop-filter */
        }
        
        .logo-container img {
            width: 40px;
            height: 40px;
            object-fit: contain;
        }
        
        .app-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: white; /* White text */
            margin-bottom: 5px;
            letter-spacing: 0.5px;
        }
        
        .app-subtitle {
            font-size: 1rem;
            color: white; /* White text */
            font-weight: 500;
        }

        .app-description {
            font-size: 0.9rem;
            color: white; /* White text */
            margin-top: 10px;
            line-height: 1.4;
        }
        
        /* Video Button */
        .video-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #deae35; /* Gold background */
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .video-btn:hover {
            background: #a8812a; /* Darker gold on hover */
            transform: scale(1.1);
        }
        
        .video-btn i {
            color: white; /* White icon */
            font-size: 1.2rem;
        }

        /* Info Button */
        .info-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #deae35; /* Gold background */
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .info-btn:hover {
            background: #a8812a; /* Darker gold on hover */
            transform: scale(1.1);
        }
        
        .info-btn i {
            color: white; /* White icon */
            font-size: 1.2rem;
        }
        
        /* Arabic Letter Selectors */
        .letter-selectors {
            display: flex;
            justify-content: center;
            gap: 15px;
            padding: 20px;
            background: #F5F5F5; /* White background */
        }
        
        .letter-selector {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 120px;
        }
        
        .letter-label {
            font-size: 1.2rem;
            color: black; /* Black text */
            margin-bottom: 15px;
            font-weight: 600;
            font-family: 'Droid Arabic Naskh', 'Noto Sans Arabic', serif;
        }
        
        .letter-wheel {
            position: relative;
            height: 180px;
            width: 100px;
            overflow: hidden;
            border-radius: 15px;
            background: transparent; /* Removed background */
            box-shadow: none; /* Removed shadow */
            border: none; /* Removed border */
        }
        
        .letter-option {
            position: absolute;
            width: 100%;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2.5rem;
            font-family: 'Droid Arabic Naskh', 'Noto Sans Arabic', serif;
            color: black; /* Black text */
            transition: all 0.1s ease-out;
            opacity: 0.15; /* Faded unselected letters */
            transform: scale(0.8);
            user-select: none;
        }
        
        .letter-option.prev {
            top: 0;
            z-index: 1;
        }
        
        .letter-option.selected {
            top: 50%;
            transform: translateY(-50%) scale(1);
            opacity: 1; /* Full opacity for selected */
            z-index: 3;
            font-weight: bold;
            font-size: 3rem;
            color: black; /* Black text */
        }
        
        .letter-option.next {
            bottom: 0;
            z-index: 1;
        }
        
        /* Root Word Display */
        .root-display {
            padding: 15px;
            background: transparent;
            text-align: center;
            border: none;
            box-shadow: none;
            margin: 0 20px;
            border-radius: 10px;
        }
        
        .root-word {
            font-size: 3.5rem;
            font-family: 'Droid Arabic Naskh', 'Noto Sans Arabic', serif;
            color: black; /* Black text */
            font-weight: bold;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* Search and Dictionary Buttons */
        .controls {
            display: flex;
            justify-content: space-around;
            gap: 10px;
            padding: 15px 20px 10px;
            background: #F5F5F5; /* White background */
        }
        
        #searchBtn, #dictionaryBtn {
            flex: 1;
            padding: 16px 20px;
            background: #deae35; /* Gold background */
            color: white; /* White text */
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(222, 174, 53, 0.3); /* Gold shadow */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        #searchBtn:hover, #dictionaryBtn:hover {
            background: #a8812a; /* Darker gold on hover */
            transform: translateY(-2px);
        }

        /* Verb Forms Display - 2x2 Grid */
        .verb-forms {
            display: flex;
            flex-wrap: wrap;
            padding: 15px;
            background: #F5F5F5; /* White background */
            gap: 12px;
            justify-content: center;
            margin-top: -5px;
        }
        
        .verb-form {
            flex: 0 0 calc(50% - 6px);
            min-width: 180px;
            background: white;
            border-radius: 15px;
            padding: 18px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
            border: 2px solid #deae35; /* Gold border */
        }
        
        .verb-title {
            font-size: 1rem;
            color: black; /* Black text */
            margin-bottom: 12px;
            font-weight: 600;
        }
        
        .verb-word {
            font-size: 2.2rem;
            font-family: 'Droid Arabic Naskh', 'Noto Sans Arabic', serif;
            color: black; /* Black text */
            font-weight: bold;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* Footer Links */
        .footer-links {
            padding: 15px 20px;
            background: #F5F5F5; /* White background */
            text-align: center;
            border-top: 1px solid #deae35; /* Gold border */
            display: flex;
            justify-content: center;
            gap: 40px;
        }

        .footer-links a {
            color: black; /* Black link color */
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #deae35; /* Gold on hover */
        }

        /* Modals (General) */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow: auto;
            position: relative;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
            border: 2px solid #deae35; /* Gold border added to modal content */
        }
        
        .modal-header {
            background: black; /* Solid Black background */
            padding: 20px;
            color: white; /* White text */
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .modal-body {
            padding: 30px;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 1.8rem;
            color: white; /* White icon */
            cursor: pointer;
            background: none;
            border: none;
        }
        
        /* Video Modal Specific Styles */
        .video-container {
            position: relative;
            width: 100%;
            padding-bottom: 56.25%; /* 16:9 aspect ratio for video */
            background: #000;
        }
        
        .video-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            color: white;
            background: #222;
        }
        
        .video-placeholder i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #deae35; /* Gold icon */
        }
        
        .video-placeholder p {
            font-size: 1.2rem;
            text-align: center;
            max-width: 80%;
        }
        /* YouTube iframe styling */
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Info Modal Specific Styles */
        .info-modal-image {
            max-width: 80%; /* Reduced max-width */
            height: auto;
            border-radius: 10px;
            margin: 0 auto 20px; /* Centered image */
            display: block; /* Ensures margin auto works for centering */
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .info-modal-text {
            text-align: center;
            font-size: 0.95rem;
            line-height: 1.5;
            color: #555;
        }

        .info-modal-text strong {
            color: #333;
        }

        .info-modal-text a {
            color: #deae35; /* Gold link color */
            text-decoration: none;
            font-weight: 600;
        }

        .info-modal-text a:hover {
            text-decoration: underline;
        }
        
        /* Mobile Responsiveness: Styles applied when screen width is 480px or less */
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            /* Header adjustments */
            .header {
                padding: 15px;
            }
            .app-title {
                font-size: 1.5rem;
            }
            .app-subtitle {
                font-size: 0.85rem;
            }
            .app-description {
                font-size: 0.8rem;
            }
            
            /* Header button adjustments */
            .video-btn, .info-btn {
                width: 35px;
                height: 35px;
                top: 10px;
                right: 10px;
            }
            .info-btn {
                left: 10px;
            }

            .letter-selectors {
                gap: 8px;
                padding: 15px 10px;
            }
            
            .letter-selector {
                width: 100px;
            }
            
            .letter-wheel {
                height: 150px;
                width: 90px;
            }
            
            .letter-option {
                height: 50px;
                font-size: 2rem;
            }
            
            .letter-option.selected {
                font-size: 2.5rem;
            }
            
            .letter-label {
                font-size: 1.1rem;
            }
            
            /* Root word display adjustments */
            .root-display {
                padding: 10px;
                margin: 0 10px;
            }
            .root-word {
                font-size: 2.2rem;
                height: 50px;
            }
            
            /* Button adjustments */
            .controls {
                padding: 10px;
                gap: 8px;
            }
            #searchBtn, #dictionaryBtn {
                padding: 12px 15px;
                font-size: 1rem;
            }

            /* Verb Forms Display (Results) adjustments for 2x2 grid */
            .verb-forms {
                gap: 8px;
                padding: 10px;
            }
            
            .verb-form {
                flex: 0 0 calc(50% - 4px);
                min-width: unset;
                padding: 10px;
            }
            
            .verb-title {
                display: none;
            }
            .verb-word {
                font-size: 1.5rem;
                height: 40px;
            }

            .modal-body {
                padding: 20px;
                font-size: 1rem;
            }

            .modal-header {
                font-size: 1.3rem;
                padding: 15px;
            }

            .close-modal {
                font-size: 1.5rem;
                top: 10px;
                right: 15px;
            }

            .footer-links {
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="video-btn" id="videoBtn">
                <i class="fas fa-play"></i>
            </button>
            <button class="info-btn" id="infoBtn">
                <i class="fas fa-question"></i>
            </button>
            <div class="logo-container">
                <a href="https://khuddam.co.nz/" target="_blank"> <!-- Logo made clickable -->
                    <img src="https://khuddam.co.nz/images/khuddam-logo-white.png" alt="Logo">
                </a>
            </div>
            <h1 class="app-title">Digital Qur'anic Dictionary</h1>
            <p class="app-subtitle">By Late Lutfur Rahman Khan Sb</p>
            <p class="app-description">This digital dictionary is free of cost for the purpose of sadaqah-e-jariyah (continuous charity).</p>
        </div>
        
        <div class="letter-selectors">
            <div class="letter-selector">
                <div class="letter-label">ل كلمة</div>
                <div class="letter-wheel" id="letter3">
                    </div>
            </div>
            
            <div class="letter-selector">
                <div class="letter-label">ع كلمة</div>
                <div class="letter-wheel" id="letter2">
                    </div>
            </div>
            
            <div class="letter-selector">
                <div class="letter-label">ف كلمة</div>
                <div class="letter-wheel" id="letter1">
                    </div>
            </div>
        </div>
        
        <div class="root-display">
            <div class="root-word" id="rootWord">فعل</div>
        </div>
        
        <div class="controls">
            <button id="searchBtn">
                <i class="fas fa-search"></i>
                Search
            </button>
            <button id="dictionaryBtn">
                <i class="fas fa-book"></i>
                Dictionary
            </button>
        </div>
        
        <div class="verb-forms">
            <div class="verb-form">
                <div class="verb-title">Past</div>
                <div class="verb-word" id="pastTense">فَعَلَ</div>
            </div>
            
            <div class="verb-form">
                <div class="verb-title">Present & Future</div>
                <div class="verb-word" id="presentTense">يَفْعَلُ</div>
            </div>
            
            <div class="verb-form">
                <div class="verb-title">Imperative</div>
                <div class="verb-word" id="imperative">اِفْعَلْ</div>
            </div>
            
            <div class="verb-form">
                <div class="verb-title">Verbal Noun (Masdar)</div>
                <div class="verb-word" id="masdar">فَعْلًا</div>
            </div>
        </div>
        
        <div class="footer-links">
            <a href="#" id="fourHuroofLink">Words with 4 Huroof</a>
            <a href="#" id="notesLink">Important Notes</a>
        </div>
    </div>
    
    <div class="modal" id="dictionaryModal">
        <div class="modal-content">
            <div class="modal-header" id="dictionaryModalHeader">
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                </div>
        </div>
    </div>
    
    <div class="modal" id="videoModal">
        <div class="modal-content">
            <div class="modal-header">
                Educational Video
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="video-container">
                    <!-- YouTube video embed -->
                    <iframe src="https://www.youtube.com/embed/Io2f2PVz0UE" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
                <div style="padding: 20px; text-align: center;">
                    <h3>How to Use the Qur'anic Dictionary</h3>
                    <p>This video will explain how to effectively use the digital Qur'anic dictionary to enhance your understanding of Arabic root words.</p>
                    <p>You'll learn about the structure of Arabic verbs and how to analyze word formations.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" id="infoModal">
        <div class="modal-content">
            <div class="modal-header">
                About This Dictionary
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <img src="https://raw.githubusercontent.com/minhajsiddiqui20/arabic-dictionary/refs/heads/main/QuranicDictionary.jpg" alt="Quranic Dictionary Cover" class="info-modal-image">
                <p class="info-modal-text">
                    <strong>KHUDDAM-UL-QURAN NZ © 2021</strong><br>
                    This dictionary was written by Lutfur Rahman Sb (late) and published by Al Balagh Foundation, Lahore, Pakistan. For the sake of Allah (SWT), Khuddam-ul-Quran NZ has endeavoured to digitise it for the benefit of all Arabic grammar students, enabling a quick search for the meanings of Qur’anic words. Alhamdulillah. May Allah (SWT) accept our efforts. Ameen.<br>
                    For feedback <a href="mailto:<EMAIL>">click here</a><br>
                    <EMAIL>
                </p>
            </div>
        </div>
    </div>

    <div class="modal" id="fourHuroofModal">
        <div class="modal-content">
            <div class="modal-header">
                Words with 4 Huroof
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>This is a placeholder page for "Words with 4 Huroof".</p>
                <p>You can add detailed content here later regarding words with four root letters.</p>
            </div>
        </div>
    </div>

    <div class="modal" id="notesModal">
        <div class="modal-content">
            <div class="modal-header">
                Important Notes
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>This is a placeholder page for "Important Notes".</p>
                <p>You can add important information, grammar rules, or usage tips here later.</p>
            </div>
        </div>
    </div>

    <script>
        // AudioContext for click sound
        let audioContext;
        let clickBuffer;

        function initAudio() {
            // Ensure audio context is resumed from a user gesture for browser compatibility
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
            if (audioContext.state === 'suspended') {
                audioContext.resume();
            }

            // Create a simple click sound (e.g., a short sine wave burst)
            const duration = 0.05; // seconds
            const sampleRate = audioContext.sampleRate;
            const frameCount = sampleRate * duration;
            clickBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
            const channelData = clickBuffer.getChannelData(0);
            const frequency = 880; // Higher pitch for a distinct click
            for (let i = 0; i < frameCount; i++) {
                channelData[i] = Math.sin(2 * Math.PI * frequency * (i / sampleRate)) * (1 - (i / frameCount)); // Fade out
            }
        }

        function playClickSound() {
            if (!audioContext || !clickBuffer) {
                initAudio(); // Initialize audio context and buffer on first play attempt
            }
            if (audioContext && clickBuffer) {
                const source = audioContext.createBufferSource();
                source.buffer = clickBuffer;
                source.connect(audioContext.destination);
                source.start(0);
            }
        }


        // Arabic letters array
        const arabicLetters = [
            'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س',
            'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
        ];
        
        // Selected letters for each position
        const selectedLetters = [19, 17, 22]; // Indexes for ف, ع, ل initially (فعل)
        
        // Verb database - sample data
        const verbDatabase = {
            "فعل": {
                past: "فَعَلَ",
                present: "يَفْعَلُ",
                imperative: "اِفْعَلْ",
                masdar: "فَعْلًا"
            },
            "كتب": {
                past: "كَتَبَ",
                present: "يَكْتُبُ",
                imperative: "اُكْتُبْ",
                masdar: "كِتَابَةً"
            },
            "قرأ": {
                past: "قَرَأَ",
                present: "يَقْرَأُ",
                imperative: "اِقْرَأْ",
                masdar: "قِرَاءَةً"
            },
            "سمع": { // Added 'سمع' for verb forms
                past: "سَمِعَ",
                present: "يَسْمَعُ",
                imperative: "اِسْمَعْ",
                masdar: "سَمْعًا"
            },
            "علم": {
                past: "عَلِمَ",
                present: "يَعْلَمُ",
                imperative: "اِعْلَمْ",
                masdar: "عِلْمًا"
            },
            "نصر": {
                past: "نَصَرَ",
                present: "يَنْصُرُ",
                imperative: "اُنْصُرْ",
                masdar: "نَصْرًا"
            },
            "دخل": {
                past: "دَخَلَ",
                present: "يَدْخُلُ",
                imperative: "اُد_خُلْ",
                masdar: "دُخُولًا"
            },
            "خرج": {
                past: "خَرَجَ",
                present: "يَخْرجُ",
                imperative: "اُخْرُجْ",
                masdar: "خُرُوجًا"
            },
            "عبد": {
                past: "عَبَدَ",
                present: "يَعْبُدُ",
                imperative: "اُعْبُد_ْ",
                masdar: "عِبَادَةً"
            },
            "صبر": {
                past: "صَبَرَ",
                present: "يَصْبِرُ",
                imperative: "اِصْبِرْ",
                masdar: "صَبْرًا"
            }
        };
        
        // dictionaryContent object now holds content for dynamic display in modal
        const dictionaryContent = {
            "سمع": `
                <div style="font-family: 'Segoe UI', system-ui, -apple-system, sans-serif; direction: ltr; text-align: left; color: #333;">
                    <h3 style="font-size: 1.5rem; color: black; margin-bottom: 10px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 5px;">1. Core Meaning</h3>
                    <p style="margin-bottom: 10px;">The basic meaning is to hear or to listen.</p>
                    <p style="margin-bottom: 10px;">It can also extend to accepting or paying attention to something heard.</p>
                    <p style="margin-bottom: 10px;">In religious contexts, it can imply obedience after hearing (hearing and then complying).</p>
                    <br>
                    <h3 style="font-size: 1.5rem; color: black; margin-bottom: 10px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 5px;">2. Common Forms Derived from س م ع</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 15px; text-align: left;">
                        <thead>
                            <tr style="background-color: #f2f2f2;">
                                <th style="padding: 10px; border: 1px solid #ddd; font-weight: bold; text-align: right;">Arabic</th>
                                <th style="padding: 10px; border: 1px solid #ddd; font-weight: bold; text-align: left;">Meaning</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">سَمِيعَ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">He heard / listened</td>
                            </tr>
                            <tr>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">يَسْمَعُ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">He hears / is hearing / will hear</td>
                            </tr>
                            <tr>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">اِسْمَعْ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">Listen! (command)</td>
                            </tr>
                            <tr>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">سَمْعٌ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">Hearing (the sense of hearing)</td>
                            </tr>
                        </tbody>
                    </table>
                    <br>
                    <h3 style="font-size: 1.5rem; color: black; margin-bottom: 10px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 5px;">3. Usage of Root Letters in Different Forms</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 15px; text-align: left;">
                        <thead>
                            <tr style="background-color: #f2f2f2;">
                                <th style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Pattern</th>
                                <th style="padding: 10px; border: 1px solid #ddd; font-weight: bold; text-align: right;">Word</th>
                                <th style="padding: 10px; border: 1px solid #ddd; font-weight: bold; text-align: left;">Meaning & Explanation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">فَعِيلٌ</td>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">سَمِيعٌ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">Active participle meaning “one who hears” or “listener”; also one of the Names of Allah (The All-Hearing).</td>
                            </tr>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">فَعَّالٌ</td>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">سَمَّاعٌ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">Intensive form meaning “one who listens attentively” or “very attentive listener”.</td>
                            </tr>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">مَفْعَلٌ</td>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">مَسْمَعٌ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">A noun indicating “place of hearing” or “earshot”.</td>
                            </tr>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">مَفْعُولٌ</td>
                                <td class="arabic-text" style="padding: 10px; border: 1px solid #ddd; font-family: 'Droid Arabic Naskh', serif;">مَسْمُوعٌ</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">Passive participle meaning “heard” or “audible”.</td>
                            </tr>
                        </tbody>
                    </table>
                    <br>
                    <h3 style="font-size: 1.5rem; color: black; margin-bottom: 10px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 5px;">4. Qur’anic Meanings and References</h3>
                    <ul style="list-style-type: none; padding-left: 0;">
                        <li style="margin-bottom: 15px;">
                            <strong style="color: black;">1 – To hear</strong>
                            <p class="arabic-verse" style="margin-top: 5px; font-family: 'Noto Naskh Arabic', serif;">(24:16) وَلَوْلَا إِذْ سَمِعْتُمُوهُ قُلْتُمْ مَا يَكُونُ لَنَا أَنْ نَتَكَلَّمَ بِهَذَا</p>
                            <p class="english-translation" style="margin-top: 5px;">— if only when you heard it you had said, ‘It is not [proper] for us to speak of this!’</p>
                            <p class="arabic-verse" style="margin-top: 5px; font-family: 'Noto Naskh Arabic', serif;">(2:171) مَا لاَ يَسْمَعُ إِلَّا دُعَاءً وَنِدَاءً</p>
                            <p class="english-translation" style="margin-top: 5px;">— a dumb animal [lit. one who hears nothing but a shout and a cry]</p>
                        </li>
                        <li style="margin-bottom: 15px;">
                            <strong style="color: black;">2 – To hear of</strong>
                            <p class="arabic-verse" style="margin-top: 5px; font-family: 'Noto Naskh Arabic', serif;">(12:31) فَلَمَّا سَمِعَتْ بِمَكْرِهِنَّ أَرْسَلَتْ إِلَيْهِنَّ</p>
                            <p class="english-translation">— so when she heard of their malicious talk, she sent for them</p>
                        </li>
                        <li style="margin-bottom: 15px;">
                            <strong style="color: black;">3 – To have the faculty of hearing</strong>
                            <p class="arabic-verse" style="margin-top: 5px; font-family: 'Noto Naskh Arabic', serif;">(19:42) لِمَ تَعْبُدُ مَا لاَ يَسْمَعُ وَلَا يُبْصِرُ</p>
                            <p class="english-translation">— why do you worship something that can neither hear nor see?!</p>
                        </li>
                        <li style="margin-bottom: 15px;">
                            <strong style="color: black;">4 – To listen</strong>
                            <p class="arabic-verse" style="margin-top: 5px; font-family: 'Noto Naskh Arabic', serif;">(41:26) وَقَالَ الَّذِينَ كَفَرُوا لَا تَسْمَعُوا لِهَذَا الْقُرْءَانِ</p>
                            <p class="english-translation">— the disbelievers say, ‘Do not listen to this Qur’an’</p>
                        </li>
                        <li style="margin-bottom: 15px;">
                            <strong style="color: black;">5 – To obey / listen to</strong>
                            <p class="arabic-verse" style="margin-top: 5px; font-family: 'Noto Naskh Arabic', serif;">(36:25) إِنِّي آمَنتُ بِرَبِّكُمْ فَٱسْمَعُونِ</p>
                            <p class="english-translation">— I believe in your Lord, so listen to me / obey me</p>
                        </li>
                        <li style="margin-bottom: 15px;">
                            <strong style="color: black;">6 – To know / acknowledge</strong>
                            <p class="arabic-verse" style="margin-top: 5px; font-family: 'Noto Naskh Arabic', serif;">(58:1) قَدْ سَمِعَ ٱللَّهُ قَوْلَ ٱلَّتِي تُجَادِلُكَ فِي زَوْجِهَا وَتَشْتَكِي إِلَى ٱللَّهِ</p>
                            <p class="english-translation" style="margin-top: 5px;">— God has heard the words of the one who disputes with you concerning her husband and complains to God</p>
                        </li>
                    </ul>
                </div>
            `
        };
        
        // Initialize letter selectors
        function initLetterSelectors() {
            for (let i = 1; i <= 3; i++) {
                updateLetterSelector(i);
                setupScrolling(i);
            }
            updateRootWord();
            searchVerb(); // Call searchVerb initially to display default verb forms
        }
        
        // Update letter selector display
        function updateLetterSelector(position) {
            const container = document.getElementById(`letter${position}`);
            container.innerHTML = '';
        
            const currentIndex = selectedLetters[position - 1];
        
            // Previous letter
            const prevIndex = (currentIndex - 1 + arabicLetters.length) % arabicLetters.length;
            const prevLetter = document.createElement('div');
            prevLetter.className = 'letter-option prev';
            prevLetter.textContent = arabicLetters[prevIndex];
            container.appendChild(prevLetter);
        
            // Selected letter
            const selectedLetter = document.createElement('div');
            selectedLetter.className = 'letter-option selected';
            selectedLetter.textContent = arabicLetters[currentIndex];
            container.appendChild(selectedLetter);
        
            // Next letter
            const nextIndex = (currentIndex + 1) % arabicLetters.length;
            const nextLetter = document.createElement('div');
            nextLetter.className = 'letter-option next';
            nextLetter.textContent = arabicLetters[nextIndex];
            container.appendChild(nextLetter);
        }
        
        // Setup scrolling for letter wheels
        function setupScrolling(position) {
            const wheel = document.getElementById(`letter${position}`);
        
            wheel.addEventListener('wheel', (e) => {
                e.preventDefault(); // Prevent page scroll
                const direction = e.deltaY > 0 ? 1 : -1; // Determine scroll direction
                changeLetter(position, direction); // Change one letter at a time
            });
            
            // Touch support for mobile
            let touchStartY = 0;
            let isSwiping = false; // Flag to track if a swipe is in progress
        
            wheel.addEventListener('touchstart', (e) => {
                touchStartY = e.touches[0].clientY;
                isSwiping = true; // Set flag when touch starts
            });
        
            wheel.addEventListener('touchmove', (e) => {
                e.preventDefault(); // Prevent page scroll during touchmove
                if (!isSwiping) return; // Only process if a swipe might be happening
                const touchY = e.touches[0].clientY;
                const swipeDistance = touchY - touchStartY;
                
                // Trigger change if swipe distance is significant enough for one letter
                if (Math.abs(swipeDistance) > 30) { // Threshold for a significant swipe (adjust as needed)
                    const direction = swipeDistance > 0 ? -1 : 1; // Invert direction for intuitive swipe (swipe down = next letter)
                    changeLetter(position, direction);
                    touchStartY = touchY; // Reset start for continuous swipe
                }
            });
        
            wheel.addEventListener('touchend', () => {
                isSwiping = false; // Reset flag when touch ends
            });
        }
        
        // Change letter in selector
        function changeLetter(position, amount) {
            selectedLetters[position - 1] = (selectedLetters[position - 1] + amount + arabicLetters.length) % arabicLetters.length;
            updateLetterSelector(position);
            updateRootWord();
            playClickSound(); // Play sound on letter change
        }
        
        // Update root word display
        function updateRootWord() {
            const rootWord = arabicLetters[selectedLetters[0]] +
                                arabicLetters[selectedLetters[1]] +
                                arabicLetters[selectedLetters[2]];
            document.getElementById('rootWord').textContent = rootWord;
        }
        
        // Search for verb forms
        function searchVerb() {
            const rootWord = document.getElementById('rootWord').textContent;
            
            if (verbDatabase[rootWord]) {
                document.getElementById('pastTense').textContent = verbDatabase[rootWord].past;
                document.getElementById('presentTense').textContent = verbDatabase[rootWord].present;
                document.getElementById('imperative').textContent = verbDatabase[rootWord].imperative;
                document.getElementById('masdar').textContent = verbDatabase[rootWord].masdar;
            } else {
                // Default values for unknown roots
                document.getElementById('pastTense').textContent = '---';
                document.getElementById('presentTense').textContent = '---';
                document.getElementById('imperative').textContent = '---';
                document.getElementById('masdar').textContent = '---';
            }
        }
        
        // Open dictionary modal
        function openDictionary() {
            const rootWord = document.getElementById('rootWord').textContent;
            const dictionaryModal = document.getElementById('dictionaryModal');
            const dictionaryModalHeader = document.getElementById('dictionaryModalHeader');
            const dictionaryModalBody = dictionaryModal.querySelector('.modal-body');
        
            // Set the modal header to the current root word
            // Note: The close button is dynamically added here.
            dictionaryModalHeader.innerHTML = `
                ${rootWord.split('').join(' - ')} 
                <button class="close-modal">&times;</button>
            `;
            // Apply styles to the dynamically created header text
            dictionaryModalHeader.style.fontFamily = "'Droid Arabic Naskh', 'Noto Sans Arabic', serif";
            dictionaryModalHeader.style.fontSize = "1.8rem";
            dictionaryModalHeader.style.fontWeight = "bold";
            dictionaryModalHeader.style.color = "white";
            dictionaryModalHeader.style.background = "black"; // Ensure header background is black
            dictionaryModalHeader.style.padding = "20px"; // Ensure consistent padding

            // Check if we have specific content for this root word in dictionaryContent
            if (dictionaryContent[rootWord]) {
                dictionaryModalBody.innerHTML = dictionaryContent[rootWord];
            } else {
                // Fallback to the generic placeholder page for other words
                dictionaryModalBody.innerHTML = `
                    <p style="text-align: left; margin-bottom: 20px;">
                        Detailed content for this root word is not yet available.
                    </p>
                    <p style="text-align: left;">Please check back later or contribute to expand the dictionary!</p>
                `;
            }
        
            // Show the modal
            dictionaryModal.style.display = 'flex';
        
            // Re-attach close listener for the newly created close button in the header
            dictionaryModal.querySelector('.close-modal').addEventListener('click', closeModal);
        }
        
        // Open video modal
        function openVideo() {
            const videoModal = document.getElementById('videoModal');
            const videoContainer = videoModal.querySelector('.video-container');
            // Embed the YouTube video iframe
            videoContainer.innerHTML = `
                <iframe src="https://www.youtube.com/embed/Io2f2PVz0UE" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            `;
            document.getElementById('videoModal').style.display = 'flex';
        }

        // Open info modal
        function openInfo() {
            document.getElementById('infoModal').style.display = 'flex';
        }

        // Open 4 Huroof modal
        function openFourHuroof() {
            document.getElementById('fourHuroofModal').style.display = 'flex';
        }

        // Open notes modal
        function openNotes() {
            document.getElementById('notesModal').style.display = 'flex';
        }
        
        // Close modals
        function closeModal() {
            document.getElementById('dictionaryModal').style.display = 'none';
            document.getElementById('videoModal').style.display = 'none';
            // Stop YouTube video when modal closes
            const videoIframe = document.querySelector('#videoModal .video-container iframe');
            if (videoIframe) {
                videoIframe.src = videoIframe.src; // Reloads the iframe, effectively stopping the video
            }
            document.getElementById('infoModal').style.display = 'none';
            document.getElementById('fourHuroofModal').style.display = 'none';
            document.getElementById('notesModal').style.display = 'none';
        }
        
        // Event Listeners
        document.getElementById('searchBtn').addEventListener('click', searchVerb);
        document.getElementById('dictionaryBtn').addEventListener('click', openDictionary);
        document.getElementById('videoBtn').addEventListener('click', openVideo);
        document.getElementById('infoBtn').addEventListener('click', openInfo);
        document.getElementById('fourHuroofLink').addEventListener('click', (e) => { e.preventDefault(); openFourHuroof(); });
        document.getElementById('notesLink').addEventListener('click', (e) => { e.preventDefault(); openNotes(); });

        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', closeModal);
        });
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                closeModal();
            }
        });
        
        // Initialize on load
        window.onload = initLetterSelectors;
    </script>
</body>
</html>
